<template>
  <div>
    <div class="pageInfo">
      <span class="btn saveDraft" @click="handleSave()">
        <svg-icon icon-class="baocun"></svg-icon>
        <font>保存</font>
      </span>
      <span class="btn formReset" @click="handleFormReset()">
        <svg-icon icon-class="zhongzhi"></svg-icon>
        <font>重置</font>
      </span>
      <span class="btn optClose" @click="handleOptClose()">
        <svg-icon icon-class="close"></svg-icon>
        <font>关闭</font>
      </span>
    </div>
    <div class="message tableForm">
      <div class="orderTitle" style="font-weight: 700">新增纪检人员</div>
      <sb-el-form
        ref="appFormUser"
        :form="appFormUser"
        v-model="appFormUserValue"
        :disabled="appFormUserValue.formDisabled"
        :on-ok="handleDoFun2"
      >
      </sb-el-form>
    </div>
    <div class="flex j-s a-c m-title">
      <span>学历信息</span>
      <div class="flex a-c">
        <el-button type="primary" @click="handleAddXueli()" size="small"
          >添加</el-button
        >
      </div>
    </div>
    <el-table
      class="tableCustom"
      :data="dataListXueli"
      style="width: 100%"
      border
      :cell-style="{ background: '#ffffff' }"
    >
      <el-table-column label="开始时间" align="center">
        <template v-slot:default="scope">
          <el-date-picker
            v-model="scope.row.startTime"
            type="date"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            placeholder="请选择"
          >
          </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center">
        <template v-slot:default="scope">
          <el-date-picker
            v-model="scope.row.endTime"
            type="date"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            placeholder="请选择"
          >
          </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column label="学校名称" align="center">
        <template v-slot:default="scope">
          <el-input
            v-model="scope.row.schoolName"
            placeholder="请输入学校名称"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="专业名称" align="center">
        <template v-slot:default="scope">
          <el-input
            v-model="scope.row.major"
            placeholder="请输入专业名称"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button
            @click="handledelxueli(scope.$index)"
            type="text"
            size="small"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div class="flex j-s a-c m-title">
      <span>工作经历</span>
      <div class="flex a-c">
        <el-button type="primary" @click="handleAddWork()" size="small"
          >添加</el-button
        >
      </div>
    </div>
    <el-table
      class="tableCustom"
      :data="dataListWork"
      style="width: 100%"
      border
      :cell-style="{ background: '#ffffff' }"
    >
      <el-table-column label="开始时间" align="center">
        <template v-slot:default="scope">
          <el-date-picker
            v-model="scope.row.startTime"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="date"
            placeholder="请选择"
          >
          </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center">
        <template v-slot:default="scope">
          <el-date-picker
            v-model="scope.row.endTime"
            type="date"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            placeholder="请选择"
          >
          </el-date-picker>
        </template>
      </el-table-column>
      <el-table-column label="单位名称" align="center">
        <template v-slot:default="scope">
          <el-input
            v-model="scope.row.unitName"
            placeholder="请输入单位名称"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="职位名称" align="center">
        <template v-slot:default="scope">
          <el-input
            v-model="scope.row.position"
            placeholder="请输入职位名称"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="100">
        <template slot-scope="scope">
          <el-button @click="handledelwork(scope.row)" type="text" size="small"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { disciplineAdd, disciplineEdit, disciplineDetail } from "@/api/home";

export default {
  name: "addAdmin",
  props: {
    gps: {
      type: Object,
      default() {
        return this.$route.query;
      },
    },
    types: { type: String },
    rowData: { type: Object },
  },
  computed: {},
  data() {
    return {
      initValue: {},
      clickFlag: true, //防止多次点击
      initValue: {},
      appFormUserValue: {},
      appFormUser: {
        formDisabled: false,
        labelWidth: "150px",
        inline: true,
        formItemList: [
          {
            class: "c4",
            label: "姓名",
            key: "trueName",
            type: "user",
            readonly: true,
            mulitple: false,
            stepLoad: true,
            appendShow: true,
            rule: { required: true },
            relevancy: "trueName-name,userName-id",
            defaultProps: {
              children: "children",
              label: "name",
              isLeaf: "leaf",
            },
            handleUser: "chooseFun",
          },
          {
            class: "c12",
            label: "OA账号",
            key: "userName",
            type: "input",
            placeholder: "",
            show: false,
          },
          {
            class: "c4",
            label: "部门",
            key: "company",
            type: "input",
            rule: { required: true },
            disabled: true,
          },
          {
            class: "c4",
            label: "职位",
            key: "position",
            type: "input",
            rule: { required: true },
          },
          {
            class: "c4",
            label: "参加工作时间",
            key: "workTime",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            rule: { required: true },
          },
          {
            class: "c8",
            label: " ",
            key: "_blank",
            type: "input",
            placeholder: " ",
            disabled: true,
            show: true,
          },

          {
            class: "c12",
            label: "专业类别",
            key: "professionalCategory",
            type: "checkbox",
            dictType: "professionalCategory",
            rule: { required: true },
            max: 3,
          },
        ],
      },
      dataListXueli: [],
      dataListWork: [],
    };
  },
  created() {},
  mounted() {
    this.fetchDetail();
  },

  methods: {
    fetchDetail() {
      if (this.types == "edit") {
        disciplineDetail({
          id: this.rowData.id,
        }).then((res) => {
          this.appFormUserValue = res.data;
          this.dataListXueli = res.data.educationalBackgrounds;
          this.dataListWork = res.data.workExperiences;
          this.appFormUserValue.professionalCategory = res.data.professionalCategory?.split(",");

        });
      }
    },

    chooseFun(obj, data) {
      let myorgDisplayName = data[0].orgDisplayName.split("\\");
      this.appFormUserValue.company = myorgDisplayName[0];
    },
    handleAddXueli() {
      this.dataListXueli.push({
        startTime: "",
        endTime: "",
        schoolName: "",
        major: "",
      });
    },
    handledelxueli(index) {
      this.dataListXueli.splice(index, 1);
    },
    handleAddWork() {
      this.dataListWork.push({
        startTime: "",
        endTime: "",
        unitName: "",
        position: "",
      });
    },
    handledelwork(index) {
      this.dataListWork.splice(index, 1);
    },
    // 重置表单
    handleFormReset() {
      this.appFormUserValue = Object.assign({}, this.initValue);
    },
    // 保存草稿
    handleSave() {
      this.$refs["appFormUser"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
          if (this.clickFlag) {
            this.clickFlag = false;
            this.appFormUserValue.educationalBackgrounds = this.dataListXueli;
            this.appFormUserValue.workExperiences = this.dataListWork;
            this.appFormUserValue.professionalCategory =
              this.appFormUserValue.professionalCategory.join(",");
            if (this.types == "add") {
              disciplineAdd(this.appFormUserValue)
                .then((res) => {
                  this.clickFlag = true;
                  this.$emit("closeshowDialog");
                })
                .catch((err) => {
                  this.clickFlag = true;
                });
            } else {
              disciplineEdit(this.appFormUserValue)
                .then((res) => {
                  this.clickFlag = true;
                  this.$emit("closeshowDialog");
                })
                .catch((err) => {
                  this.clickFlag = true;
                });
            }
          }
        }
      });
    },
    handleOptClose() {
      this.$emit("closeshowDialog");
    },
    handleDoFun2(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n;
      if (obj) {
        n = this[obj[fun]].call(this, obj, data);
      } else {
        n = this[fun].call(this, data);
      }
      return n;
    },
  },
};
</script>
<style scoped>
.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid #39aef5;
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}
::v-deep .tableCustom .el-textarea__inner {
  overflow: hidden !important;
  padding: 0;
  font-size: 13px;
  min-height: 23px !important;
}

::v-deep .el-table__header-wrapper {
  height: auto;
}

::v-deep .el-radio-group {
  width: 150px;
  min-width: 150px;
}

::v-deep .el-radio__label {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
}
::v-deep .tableCustom .el-input__inner {
  height: 32px;
}
::v-deep .tableCustom .el-table__cell {
  padding: 8px 0;
}
::v-deep .tableCustom .el-input__prefix {
  top: -4px;
}
::v-deep .tableForm .el-input__suffix {
}
</style>
