import request from "@/assets/js/request";
import store from "@/store";
import util from "@/assets/js/public";

// 查询
export function selectAll(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/queryAll?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 添加
export function userAdd(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/addAdmin`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 编辑
export function userEdit(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsUser/edit`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 删除
export function userDel(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/admin/delAdmin`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 工单查询
export function usTransferQuery(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usTransfer/queryAll?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 导出
export function usTransferExportQuery(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/usTransfer/exportInfo`,
        contentType: "application/json; charset=utf-8",
        data: params,
        responseType: "blob"
    });
}
// 人员查询
export function disciplineSelectAll(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/discipline/queryAll?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 添加
export function disciplineAdd(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/discipline/addDiscipline`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 编辑
export function disciplineEdit(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/discipline/updateDiscipline`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 删除
export function disciplineDel(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/discipline/deleteDiscipline?id=${params.id}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 下载模板
export function disciplineDownloadTemplate(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/discipline/downloadTemplate`,
      contentType: "application/json; charset=utf-8",
      responseType: "blob",
      method: "get"
  });
}
// 导出
export function disciplineExportQuery(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/discipline/exportInfo`,
        contentType: "application/json; charset=utf-8",
        data: params,
        responseType: "blob"
    });
}
// 详情
export function disciplineDetail(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/discipline/queryById?id=${params.id}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 批量添加
export function disciplineSaveAll(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/discipline/saveAll`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 匹配
export function disciplineMatch(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/discipline/match`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
