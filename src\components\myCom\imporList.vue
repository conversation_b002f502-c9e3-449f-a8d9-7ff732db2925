<template>
  <div class="content">
    <!-- 业务表单 -->
    <div class="">
      <sb-el-table :table="table" @getList="getList" @handleread="handleread" :on-ok="handleDoFun2">
      </sb-el-table>

      <div style="display: flex; justify-content: flex-end; padding: 20px 0">
        <el-button @click="handleUp('no')" type="primary" size="small"
          >关闭</el-button
        >
        <el-button type="primary" @click="handleUp('yes')" size="small"
          >确认</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  blank: "blank",
};
import { uploadProcessFiles } from "@/api/public";
import { disciplineSelectAll,  } from "@/api/home";

export default {
  name: "addAdmin",
  props: {
    gps: {
      type: Object,
      default() {
        return this.$route.query;
      },
    },
    types: { type: String },
    list: { type: Array },
  },
  computed: {},
  data() {
    return {
      // 业务表单
      initValue: {},
      table: {
        modulName: "adminManage_table-工单查询", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelPosition: "right",
          labelWidth: "120px",
          formItemList: [
            
          ],
        },
        belongCompanyTypeDictValue_1: [],
        tr: [
         { id: "trueName", label: "姓名", prop: "trueName" },
          {
            id: "company",
            label: "单位",
            prop: "company",
          },
          {
            id: "position",
            label: "职位",
            prop: "position",
          },
          {
            id: "professionalCategory",
            label: "专业类别",
            prop: "professionalCategory",
          },
          {
            id: "workTime",
            label: "参加工作时间",
            prop: "workTime",
          },
        ],
        // hasSetup:true,
        // setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "120",
          fixed: "right",
          data: [
            { id: "handleread", name: "查看", fun: "handleread" },
          ],
        },
        hasOtherQueryBtn: false, //是否有其他操作
        otherQueryBtn: {
          data: [
           
            // {id: "download", name: "模板下载", fun: "download", type: "primary"},
            // {id: "handleExport", name: "导出", fun: "handleExport", type: "primary"},
          ],
        },
        hasUploadData: false,
        uploadData: {
          id: "b",
          action: `/${process.env.VUE_APP_APPCODE}/action/UsUser/importInfo?source=PC`,
          name: "导入",
          isPopup: false,
          isDialog: false,
        },
        hasPagination: false,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {
    this.fetchRowData();
  },
  methods: {
    handleread(obj){

    },
    handleUp(op) {
      if (op == "yes") {
        if(this.table.data.length == 0){
            this.$message({
              message: '请重新导入数据',
              type: 'warning'
            })
            return;
        }
        this.$emit("eventImport", this.table.data);
        this.$emit("closeshowDialog");
      
      } else {
        this.$emit("closeshowDialog");
      }
    },

    handleDoFun2(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
    fetchRowData() {
        this.getList()
    },
    getList(){
        this.table.data = this.list;
    },
    chooseFun(obj, data) {
      let myorgDisplayName = data[0].orgDisplayName.split("\\");

      this.appFormValue.belongCompanyName = myorgDisplayName[0];
      this.appFormValue.belongDepartmentName = myorgDisplayName[1];
    },
    uploadFileList(obj) {
      uploadProcessFiles(obj.formData)
        .then((res) => {
          obj.content.onSuccess(res, obj.content.file, []);
        })
        .catch((error) => {
          obj.content.onError();
        });
    },
  },
};
</script>

<style scoped>
::v-deep .el-form-item .el-form-item__label[for="applyUser"] {
  line-height: 15px !important;
}

.tableForm {
  border-left: none;
}


</style>