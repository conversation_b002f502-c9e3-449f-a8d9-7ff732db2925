<template>
  <div class="app-container">
    <sb-el-table
      :table="table"
      @getList="getList"
      @handleTodo="handleTodo"
      @updateTableData="updateTableData"
      @handleExport="handleExport"
      :on-ok="handleDoFun"
    >
      <template v-slot:BUSINESS_TITLE="{obj}">
        <span class="toDetail" @click="handleTodo(obj)">{{obj.row.BUSINESS_TITLE}}</span>
      </template>
      <template v-slot:CREATED_TIME="{obj}">
        <span>{{util.getTimeDate(obj.row.CREATED_TIME,"yyyy-MM-dd HH:mm:ss")}}</span>
      </template>
       <template v-slot:isduli="{obj}">
        <span>{{obj.row.subjectMatter == '执纪审查-独立办件'?'是':'否'}}</span>
      </template>
      <template v-slot:isxieban="{obj}">
        <span>{{obj.row.subjectMatter == '执纪审查-协助办件'?'是':'否'}}</span>
      </template>
    </sb-el-table>

    <!-- 工单详情 -->
    <el-dialog :title="dialogTitle" :visible.sync="viewD" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <work-order :key="cKey" :gps="gps"  :dialogClose="dialogClose"></work-order>
    </el-dialog>
  </div>
</template>
<script>
import WorkOrder from "@/components/WorkOrder";
import { usTransferQuery,usTransferExportQuery } from "@/api/home";
import util from "@/assets/js/public";
import {getDictList} from '@/api/public'
export default {
  name: "applicationQuery",
  components: { WorkOrder },
  data() {
    return {
      viewD: false,
      dialogTitle:'',
      gps: {
        type: "join",
        location: "",
        pmInsType: "",
      },

      cKey: 0,
      table: {
        modulName: "applicationQuery-工单查询", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "120px",
          formItemList: [
            {class: "",label: "抽调人员",key: "transferPersonnel",type: "input"},
            {class: "",label: "工作内容",key: "workContent",type: "input"},

          ],
        },
        tr: [
          {id: "orderNo",label: "文号",prop: "orderNo"},
          {id: "belongCompanyName",label: "主送单位",prop: "belongCompanyName",width: 150},
          {id: "applyTime",label: "抽调时间",prop: "applyTime",width: 150},
          {id: "dayDiff",label: "天数",prop: "dayDiff",width: 150},
          {id: "transferPersonnel",label: "抽调人员",prop: "transferPersonnel",width: 150},
          {id: "nums",label: "人数",prop: "nums",width: 150},
          {id: "workContent",label: "工作内容",prop: "workContent",width: 150},
          {id: "clueNum",label: "信访线索编号",prop: "clueNum",width: 150},
          {id: "isduli",label: "是否独立办案",prop: "isduli",width: 150,show: "template" ,template:'isduli'},
          {id: "isxieban",label: "是否协办",prop: "isxieban",width: 150,show: "template" ,template:'isxieban'},
          {id: "applyUserName",label: "申请人",prop: "applyUserName",width: 150},
          // {id: "CREATED_TIME",label: "申请时间",prop: "CREATED_TIME",width: 200, show: "template" },
        ],
        // hasSetup:true,
				// setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "80",
          fixed: "right",
          data: [
            {id: "handleTodo",name: "查看", fun:"handleTodo"},
          ],
        },
        hasOtherQueryBtn: true, //是否有其他操作
        otherQueryBtn: {
          data: [
            {id: "export",type: "primary",name: "导出",fun: "handleExport"}
          ]
        },
        hasPagination: true,
        listQuery: {size: 10,page: 1},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      lineTypeList:[]
    };
  },
  created() {
    // this.getLineType()
    this.getList();
  },
  methods: {
    getLineType(){
      getDictList('lineType').then(res=>{
        this.lineTypeList = res.data;
        this.table.queryForm.formItemList[this.table.queryForm.formItemList.length - 1].options = res.data;
      })

    },
    handleType(value){
      let name = ''
      this.lineTypeList?.forEach(el=>{
        if(el.value == value){
          name = el.name;
        }
      })
      return name;

    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      if(listQuery && !listQuery.applyTime){
        listQuery.startDate = ''
        listQuery.endDate = ''
      }
      usTransferQuery(listQuery || this.table.listQuery).then((res) => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
        }).catch((err) => {
          this.table.loading = false;
        });
    },
    
    // 查看
    handleTodo(obj) {
      // 参数
      this.gps = {
        type: "join",
        location: `${process.env.VUE_APP_APPCODE}.`,
        pmInsId: obj.row.pmInsId,
        pmInsType:'A',
        isQuery: true,
      };
      var th = this.util.appNameTH(obj.row.pmInsType);
      this.dialogTitle = th.type + (obj.row.title || "") + "-查看";
      this.cKey++;
      this.viewD = true;
    },

    // 导出
    handleExport(){
      usTransferExportQuery(this.table.listQuery).then(res => {
        if(res.data){
          this.util.blobDownload(res.data,res.filename);
        }else{
          this.$message({
            message: '导出失败',
            type: 'warning',
            duration: 1500
          });
        }
		  });
    },
   
    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header{text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;}
::v-deep .el-dialog__title{color: black !important;font-size: 15.5px;}
::v-deep .el-dialog__headerbtn .el-dialog__close{
  color: black;
}
</style>