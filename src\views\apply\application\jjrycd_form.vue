<template>
  <div class="app-container">
    <sb-el-table
        :table="table"
        @getList="getList"
        @handleDel="handleDel"
        @handleEdit="handleEdit"
        @handleAdd="handleAdd"
        @download="download"
        @handleExport="handleExport"
        @getPopup="getPopup"
        :on-ok="handleDoFun"
    >
      <template v-slot:modifiedTime="{obj}">
        <span>{{util.getTimeDate(obj.row.modifiedTime,"yyyy-MM-dd HH:mm:ss")}}</span>
      </template>
        <template v-slot:type="{obj}">
        <span>{{handleType(obj.row.type)}}</span>
      </template>
    </sb-el-table>

    <!-- 工单详情 -->
    <el-dialog :title="title" :visible.sync="viewD" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <addJJUser :key="cKey" :types="types" :rowData="rowdata"  @closeshowDialog="closeshowDialog"></addJJUser>
    </el-dialog>

    <!-- 导入 -->
    <el-dialog title="导入列表" :visible.sync="viewimport" v-dialogDrag :close-on-click-modal="false" append-to-body width="50%">
      <importList :key="dKey"  @eventImport="geteventImport" :list="inportList"  @closeshowDialog="closeshowImport"></importList>
    </el-dialog>

  </div>
</template>
<script>
import addJJUser from "@/components/myCom/addJJUser";
import importList from "@/components/myCom/imporList";
import { disciplineSelectAll, disciplineAdd, disciplineEdit, disciplineDel, disciplineDownloadTemplate,disciplineExportQuery,disciplineSaveAll } from "@/api/home";
import {getDictList} from '@/api/public'


export default {
  name: "adminManage_table",
  components: {addJJUser,importList},
  data() {
    return {
      viewimport:false,
      dKey:0,
      inportList:[],
      viewD: false,
      cKey: 0,
      title: '',
      types: '',
      table: {
        modulName: "adminManage_table-工单查询", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelPosition: 'right',
          labelWidth: "120px",
          formItemList: [
            {label: "姓名", key: "trueName", type: "input",},
            {label: "单位", key: "belongCompanyCode", type: "select",dictType: "company", },

          ],
        },
        belongCompanyTypeDictValue_1: [],
        tr: [
          { id: "trueName", label: "姓名", prop: "trueName" },
          {
            id: "company",
            label: "单位",
            prop: "company",
          },
          {
            id: "position",
            label: "职位",
            prop: "position",
          },
          {
            id: "professionalCategory",
            label: "专业类别",
            prop: "professionalCategory",
          },
          {
            id: "workTime",
            label: "参加工作时间",
            prop: "workTime",
          },
        ],
        // hasSetup:true,
        // setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "120",
          fixed: "right",
          data: [
            {id: "handleEdit", name: "编辑", fun: "handleEdit"},
            {id: "handleDel", name: "删除", fun: "handleDel"},
          ],
        },
        hasOtherQueryBtn: true, //是否有其他操作
        otherQueryBtn: {
          data: [
            {id: "handleAdd", name: "新增", fun: "handleAdd", type: "primary"},
            {id: "download", name: "模板下载", fun: "download", type: "primary"},
            {id: "handleExport", name: "导出", fun: "handleExport", type: "primary"},

          ]
        },
        hasUploadData:true,
        uploadData:{
            id: "b",
            action: `/${process.env.VUE_APP_APPCODE}/action/discipline/importExcel?source=PC`,
            name: "导入",
            isPopup: true,
            isDialog: false,
        },
        hasPagination: true,
        listQuery: {size: 10, page: 1},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      rowdata: {},
      typeList:[]
    };
  },
  created() {
    this.getList();
  },
  methods: {
     getType(){
      getDictList('adminType').then(res=>{
        this.typeList = res.data;
        this.table.queryForm.formItemList[1].options = res.data;
      })

    },
    handleType(value){
      let name = ''
      this.typeList?.forEach(el=>{
        if(el.value == value){
          name = el.name;
        }
      })
      return name;

    },
     // 下载
    download() {
      disciplineDownloadTemplate()
        .then((res) => {
          this.util.blobDownload(res.data, res.filename);
        })
        .catch((error) => {});
    },
    handleImport(){

    },
    handleExport() {
      disciplineExportQuery(this.table.listQuery).then(res => {
        if (res.data) {
          this.util.blobDownload(res.data, res.filename);
        } else {
          this.$message({
            message: '导出失败',
            type: 'warning',
            duration: 1500
          });
        }
      });
    },
    // 导入
    getPopup(data){
      console.log(data)
      this.inportList = data;
      this.dKey++;
      this.viewimport = true;
    },
    getEvent(value) {
      if (this.types == 'add') {
        disciplineAdd(value).then(res => {
          this.getList();
        })
      } else {
        console.log(value)
        delete value.createdTime;
        delete value.modifiedTime;
        disciplineEdit(value).then(res => {
          this.getList();
        })
      }

    },
    geteventImport(data){
      disciplineSaveAll(data).then(res => {
        this.getList();
      })

    },
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      disciplineSelectAll(listQuery || this.table.listQuery).then((res) => {
        this.table.loading = false;
        this.table.data = res.data.content;
        this.table.total = res.data.totalElements;
      }).catch((err) => {
        this.table.loading = false;
      });
    },

    // 删除
    handleDel(data) {
      this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定", cancelButtonText: "取消", type: "warning",
      }).then(() => {
        disciplineDel({id: data.row.id}).then(res => {
          this.getList()
        })
      }).catch(() => {

      });
    },

    // 新增
    handleAdd() {
      this.cKey++;
      this.viewD = true;
      this.title = '新增'
      this.types = 'add'
    },

    // 编辑
    handleEdit(data) {
      this.rowdata = data.row
      this.cKey++;
      this.viewD = true;
      this.title = '修改'
      this.types = 'edit'
    },


    // 关闭弹框
    closeshowDialog() {
      this.viewD = false;
      this.getList();
    },
     // 关闭弹框
    closeshowImport() {
      this.viewimport = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    },
    closeshowDialog() {
      this.viewD = false;
      this.getList();
 
    }
  }
};
</script>
<style scoped>
::v-deep .el-form-item__content {
  flex: 1;
}
</style>