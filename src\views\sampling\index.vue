<template>
  <div :class="gps.location ? 'w99' : 'p10'">
    <process-btn
      ref="processBtn"
      :gps="gps"
      :processBtn="processBtn"
      :formData="appFormValue"
      :dialogClose="dialogClose"
      :on-ok="handleDoFun"
    ></process-btn>
    <!-- 文本渲染模块 -->
    <div class="cont" v-if="isPing">
      <h4 class="myh4">纪检人员抽调单</h4>
      <div class="mytextarea">
        <span class="myspan">{{ appFormValue.belongCompanyName }}</span>
        <div>
          <div
            class="mycontent"
            v-html="util.htmlDecode(appFormValue.content)"
          ></div>
        </div>

        <div style="display: flex; justify-content: flex-end">
          <div
            style="
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
            "
            class="myspan"
          >
            <span>河南省分公司纪委办公室</span>
            <span>{{ transform(appFormValue.applyTime.slice(0, 10)) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 业务表单 -->
    <div v-if="!isPing">
      <div class="message tableForm">
        <div class="orderTitle" v-if="gps.pmInsType !='B'" style="font-weight: 700">新增抽调申请</div>
        <sb-el-form
          ref="appForm"
          :form="appForm"
          v-model="appFormValue"
          :disabled="appForm.formDisabled"
          :on-ok="handleDoFun"
        >
        </sb-el-form>
      </div>
      <div class="flex j-s a-c m-title">
        <span>抽调人员选择</span>
        <div class="flex a-c">
          <el-button
            v-if="
              !gps.location || gps.type == 'draft' || 
              (gps.type == 'task' && gps.location == 'jjrycd.start')
            "
            type="primary"
            @click="handleSmartMatch"
            size="small"
            >智能配对</el-button
          >
          <el-button
            type="primary"
            v-if="
              !gps.location || gps.type == 'draft' || 
              (gps.type == 'task' && gps.location == 'jjrycd.start')
            "
            @click="addChooseUser"
            size="small"
            >添加人员</el-button
          >
        </div>
      </div>
      <sb-el-table :table="table" @handleDel="handleDel" :on-ok="handleDoFun">
        <template v-slot:modifiedTime="{ obj }">
          <span>{{
            util.getTimeDate(obj.row.modifiedTime, "yyyy-MM-dd HH:mm:ss")
          }}</span>
        </template>
        <template v-slot:type="{ obj }">
          <span>{{ handleType(obj.row.type) }}</span>
        </template>
      </sb-el-table>
    </div>

    <!-- 评价 -->
    <div v-if="gps.pmInsType =='B'">
       <div class=" j-s a-c m-title" style="margin-bottom: 0px;">
        <span>抽调人员评价</span>
       
      </div>
       <div class="message tableForm" style="margin-top:0;">
        <sb-el-form
          ref="appForm2"
          :form="appForm2"
          v-model="appFormValue2"
          :disabled="appForm2.formDisabled"
          :on-ok="handleDoFun"
        >
        </sb-el-form>

       </div>
      


    </div>

    <!-- 工单详情 -->
    <el-dialog
      title="选择抽调人员"
      :visible.sync="viewD"
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
      width="50%"
    >
      <addUser
        :key="cKey"
        :list="table.data"
        :rowData="rowdata"
        @event="getEvent"
        @closeshowDialog="closeshowDialog"
      ></addUser>
    </el-dialog>
  </div>
</template>
<script>
import addUser from "@/components/myCom/addUser";
import store from "@/store";
import util from "@/assets/js/public";
import ProcessBtn from "@/components/Process/ProcessBtn";
import { getDictList } from "@/api/public";
import { deleteDraft } from "@/api/process";
import {
  getProcessInfo,
  getFormDetail,
  saveDraft,
  startProcess,
  usTransferDetail
} from "@/api/apply/application";

let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  // applyUser: store.getters.user.truename,
  // applyUserName: store.getters.user.username,
  // belongCompanyName: store.getters.user.belongCompanyName,
  // belongDepartmentName: store.getters.user.belongDepartmentName,
  // applyTime: util.getNow(),
  blank: "blank",
};

export default {
  name: "application",
  components: { ProcessBtn, addUser },
  props: {
    href: {
      type: Object,
      default() {
        return {};
      },
    },
    // 关闭
    dialogClose: {
      type: Function,
    },
  },
  data() {
    return {
      viewD: false,
      cKey: 0,
      rowdata: {},
      gps: this.href,
      processDefKey: "",
      processBtn: {
        optClose: false,
      },

      processD: false,
      pnKey: 0,
      clickFlag: true, //防止多次点击

      nowTime: this.util.getNow("yyyy-MM-dd"),

      // 业务表单
      initValue: {},
      appFormValue2:{},
      appForm2: {
        formDisabled: false,
        labelWidth: "250px",
        inline: true,
        formItemList: [
          {
            class: "c12",
            label: "请对抽调人员胜任情况进行评价",
            key: "state",
            type: "radio",
            // dictType: "professionalCategory",
            options: [
              { name: "优秀（1分）", value: "1" },
              { name: "良好（0.8分）", value: "2" },
              { name: "一般（0.6分）", value: "3" },
              { name: "差（0.2分）", value: "4" },

            ],
            rule: { required: true },
          },
        ],
      },

      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: false,
        labelWidth: "180px",
        inline: true,
        formItemList: [
          {
            class: "c4",
            label: "标题",
            key: "title",
            type: "input",
            disabled: true,
            show: false,
          },
          {
            class: "c4",
            label: "发起人",
            key: "applyUserName",
            type: "input",
            disabled: true,
            rule: { required: true },
          },
          {
            class: "c4",
            label: "发起人OA",
            key: "applyUser",
            type: "input",
            disabled: true,
            show: false,
          },
          {
            class: "c4",
            label: "所属单位",
            key: "belongCompanyName",
            type: "input",
            disabled: true,
            rule: { required: true },
          },
          {
            class: "c4",
            label: "申请部门",
            key: "belongDepartmentName",
            type: "input",
            disabled: true,
            show: false,
          },
          {
            class: "c4",
            label: "发起时间",
            key: "applyTime",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            disabled: true,
            rule: { required: true },
          },
          {
            class: "c4",
            label: "抽调开始时间",
            key: "transferStart",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            changeFun: "handleTransferTimeStart",
            rule: { required: true },
          },
          {
            class: "c4",
            label: "抽调结束时间",
            key: "transferEnd",
            changeFun: "handleTransferTimeEnd",
            type: "date",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
            rule: { required: true },
          },
          {
            class: "c4",
            label: "抽调事由",
            key: "subjectMatter",
            type: "select",
            dictType: "transferMatter",
            rule: { required: true },
            changeFun: "handleSubjectMatter",
          },
          {
            class: "c12",
            label: "信访/线索或案件编号",
            key: "clueNum",
            type: "input",
            rule: { required: true },
            show: false,
          },
          {
            class: "c12",
            label: "工作内容",
            key: "workContent",
            type: "input",
            inputType: "textarea",
            rule: { required: true },
            show: false,
          },

          {
            class: "c12",
            label: "需求专业",
            key: "professionalCategoryList",
            type: "checkbox",
            dictType: "professionalCategory",
            rule: { required: true },
            changeFun: "handleProfessional",
            max: 3,
          },
        ],
      },
      table: {
        padding: true,
        modulName: "adminManage_table-工单查询", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelPosition: "right",
          labelWidth: "120px",
          formItemList: [
            { label: "姓名", key: "trueName", type: "input" },
            {
              label: "公司名称",
              key: "belongCompanyName",
              type: "select",
              dictType: "compName",
            },
          ],
        },
        belongCompanyTypeDictValue_1: [],
        tr: [
          { id: "trueName", label: "姓名", prop: "trueName" },
          { id: "companyName", label: "单位", prop: "companyName" },
          { id: "position", label: "职位", prop: "position" },
          {
            id: "professionalCategory",
            label: "专业类别",
            prop: "professionalCategory",
          },
          { id: "workTime", label: "参加工作时间", prop: "workTime" },
        ],
        // hasSetup:true,
        // setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "120",
          fixed: "right",
          data: [
            // {id: "handleEdit", name: "修改", fun: "handleEdit"},
            { id: "handleDel", name: "删除", fun: "handleDel" },
          ],
        },
        hasOtherQueryBtn: false, //是否有其他操作
        otherQueryBtn: {
          data: [
            {
              id: "handleAdd",
              name: "新增",
              fun: "handleAdd",
              type: "primary",
            },
            // {id: "download", name: "模板下载", fun: "download", type: "primary"},
            // {id: "handleExport", name: "导出", fun: "handleExport", type: "primary"},
          ],
        },
        hasUploadData: false,
        uploadData: {
          id: "b",
          action: `/${process.env.VUE_APP_APPCODE}/action/UsUser/importInfo?source=PC`,
          name: "导入",
          isPopup: false,
          isDialog: false,
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      taskTypeList: [],
      workNatureList: [],
      // 频次
      num1: 1,
      num2: 1,
      frequencyList: [
        { label: "日", value: "日" },
        { label: "周", value: "周" },
        { label: "月", value: "月" },
        { label: "年", value: "年" },
      ],
      frequencyValue: "日",
      isPing: false,
    };
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps, query);
    // console.log("gps", JSON.parse(JSON.stringify(this.gps)));

    this.initValue = {
      applyUserName: this.$store.getters.user.truename,
      applyUser: this.$store.getters.user.username,
      belongCompanyName: this.$store.getters.user.belongCompanyName,
      belongDepartmentName: this.$store.getters.user.belongDepartmentName,
      applyTime: this.nowTime,
    };
    this.appFormValue = Object.assign(defaultAppFormValue, this.initValue);

    this.initFun(); //初始化
  },
  methods: {
    handleTransferTimeStart(obj,value){
      console.log(value);
      this.table.data = []
    },
    handleTransferTimeEnd(obj,value){
      console.log(value);
      this.table.data = []
    },
    handleProfessional(obj,value){
      console.log(value);
       this.table.data = []

    },
    handleSubjectMatter(obj, value) {
      console.log(value);
      if (value == "执纪审查-独立办件" || value == "执纪审查-协助办件") {
        this.appForm.formItemList.forEach((item) => {
          if (item.key == "clueNum") {
            item.show = true;
            item.label = "信访/线索或案件编号";
            item.rule.required = true;
          }
          if (item.key == "workContent") {
            item.show = false;
            item.label = "工作内容";
            item.rule.required = false;
          }
        });
      }
      if (value == "监督工作") {
        this.appForm.formItemList.forEach((item) => {
          if (item.key == "clueNum") {
            item.show = true;
            item.label = "监督项目编号";
            item.rule.required = true;
          }
          if (item.key == "workContent") {
            item.show = true;
            item.label = "监督项目内容";
            item.rule.required = true;
          }
        });
      }
      if (value == "其他工作") {
        this.appForm.formItemList.forEach((item) => {
          if (item.key == "clueNum") {
            item.show = false;
            item.label = "信访/线索或案件编号";
            item.rule.required = false;
          }
          if (item.key == "workContent") {
            item.show = true;
            item.label = "工作内容";
            item.rule.required = true;
          }
        });
      }
    },
    getEvent(data) {
      console.log(data);
      this.table.data = data;
      this.table.data.forEach(ele=>{
        ele.companyName = ele.company;
      })
    },
    handleDel(obj, index) {
      console.log(obj);
      this.table.data.splice(obj.index, 1);
    },
    getList(listQuery) {},
    // 初始化
    initFun() {
      if(this.gps.pmInsType == 'A' || !this.gps.location){
        this.processDefKey = 'Process_1752718607927'
      }else if(this.gps.pmInsType == 'B'){
        this.processDefKey = 'Process_1753146173194'
      }
      this.gps.processDefKey = this.processDefKey;

      // 起草及草稿不显示“工单编号”
      // if(!this.gps.location || this.gps.location=="wgrcsw.start"){
      // 	var index = this.appForm.formItemList.findIndex(item => item.key==="workNumber");
      // 	if(index > -1){
      // 		this.appForm.formItemList[index].show = false;
      // 	}
      // 	this.appFormValue = JSON.parse(JSON.stringify(this.appFormValue));
      // }
      // 加载表单
      if (this.gps.location || (this.gps.action && this.gps.action == "read")) {
        this.loadForm();
      }else{
        this.gps.pmInsType = 'A';
        this.appForm2.pmInsType = 'A';

      }
    },

    // 获取工单详情
    loadForm() {
     
     
      var urlApi = '';
       if(this.gps.type =='join' && this.isQuery){
        // 统计查询进入
        urlApi = usTransferDetail;
         var data = {
            pmInsId: this.gps.pmInsId,
         }
      }else{
        urlApi = getFormDetail;
         var data = {
            pmInsId: this.gps.pmInsId,
            processDefKey: this.gps.processDefKey,
            pmInsType: this.gps.pmInsType,
            activityDefId: this.gps.location,
            type: this.gps.type,
          };
      }
      urlApi(data).then((res) => {
        this.appFormValue = res.data;
        this.table.data = res.data.transferStaffs;
        if (
          this.gps.location == "Activity_00xgcll" || this.gps.location == "Flow_0kwx6ur_copy"
        ) {
          this.isPing = true;
        }
        this.handleSubjectMatter({},res.data.subjectMatter)
        this.appFormValue.professionalCategoryList = this.appFormValue.professionalCategory?.split(',')

         if(this.gps.pmInsType =='B'){
          this.appFormValue2.state = res.data.state;
         }

        // 设置只读
        if (
          (this.gps.type != "draft" && this.gps.type != "task") ||
          this.gps.location != "jjrycd.start"
        ) {
          this.appForm.formDisabled = true;
          this.table.hasOperation = false;
        } else {
          this.appForm.formDisabled = false;
          this.appForm2.formDisabled = false;

        }
      });
    },

    // 重置表单
    handleFormReset() {
      this.appFormValue = Object.assign(defaultAppFormValue, this.initValue);
    },

    beforeSubmit() {},

    // 流转下一步
    handleNextBtn() {
      if(this.isPing){
        console.log(this.appFormValue);

          this.$refs["processBtn"].doProcessNext();

      }else{
        this.$refs["appForm"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
          if (this.table.data.length == 0) {
            this.$message({
              message: "至少选择一条抽调人员",
              type: "warning",
              duration: 1500,
            });
            return false;
          }
          this.appFormValue.transferStaffs = JSON.parse(
            JSON.stringify(this.table.data)
          );
          this.appFormValue.transferStaffs.forEach((item) => {
            item.inspectionId = item.id;
          });
          var professionalCategory = JSON.parse(JSON.stringify(this.appFormValue.professionalCategoryList))
          this.appFormValue.professionalCategory =
            professionalCategory?.join(",");
          console.log(this.appFormValue);
          if(this.gps.pmInsType =='B'){
            if(!this.appFormValue2.state){
              this.$message({
                message: "请对抽调人员胜任情况进行评价",
                type: "warning",
              });
              return false;
            }else{
              this.appFormValue.state = this.appFormValue2.state;
            }
          }
          this.$refs["processBtn"].doProcessNext();
        }
      });

      }
      
    },

    // 保存草稿
    handleSaveDraft() {
      this.$refs["appForm"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
          if (this.clickFlag) {
            this.clickFlag = false;
            this.appFormValue.transferStaffs = JSON.parse(
              JSON.stringify(this.table.data)
            );
            this.appFormValue.transferStaffs.forEach((item) => {
              item.inspectionId = item.id;
            });
             var professionalCategory = JSON.parse(JSON.stringify(this.appFormValue.professionalCategoryList))
            this.appFormValue.professionalCategory =
             professionalCategory?.join(",");

            saveDraft({
              processDefKey: this.processDefKey,
              title: this.appFormValue.title,
              formData: this.appFormValue,
            })
              .then((res) => {
                this.clickFlag = true;
                if (!this.gps.location) {
                  this.$router.push({ name: "processDraft" });
                } else {
                  this.dialogClose();
                }
              })
              .catch((err) => {
                this.clickFlag = true;
              });
          }
        }
      });
    },

    // 废除草稿
    handleAbolish() {
      if (this.clickFlag) {
        this.clickFlag = false;
        deleteDraft({
          pmInsId: this.gps.pmInsId,
          processDefKey: this.processDefKey,
        })
          .then((res) => {
            this.clickFlag = false;
            this.dialogClose();
          })
          .catch((err) => {
            this.clickFlag = true;
          });
      }
    },
    transform(dateStr) {
      var date = new Date(dateStr);
      var year = date.getFullYear(); // 获取四位数的年份
      var month = date.getMonth() + 1; // 月份从0开始，+1并补零到两位
      var day = date.getDate(); // 日补充前导零

      var formattedDate = year + "年" + month + "月" + day + "日";
      return formattedDate;
    },
    // 添加人员
    addChooseUser() {
      if (!this.appFormValue.transferStart || !this.appFormValue.transferEnd) {
        this.$message({
          message: "请先选择抽调开始和结束时间",
          type: "warning",
          duration: 1500,
        });
        return false;
      }
      this.rowdata = {
        startTime: this.appFormValue.transferStart,
        endTime: this.appFormValue.transferEnd,
        professionalCategory: this.appFormValue.professionalCategoryList?.join(","),
      };
      this.cKey++;
      this.viewD = true;
    },
    // 智能配对
    handleSmartMatch() {
      if (!this.appFormValue.transferStart || !this.appFormValue.transferEnd) {
        this.$message({
          message: "请先选择抽调开始和结束时间",
          type: "warning",
          duration: 1500,
        });
        return false;
      }
      if (
        !this.appFormValue.professionalCategoryList  ||
        this.appFormValue.professionalCategoryList.length == 0
      ) {
        this.$message({
          message: "请先选择需求专业",
          type: "warning",
          duration: 1500,
        });
        return false;
      }
      this.rowdata = {
        startTime: this.appFormValue.transferStart,
        endTime: this.appFormValue.transferEnd,
        professionalCategory: this.appFormValue.professionalCategoryList?.join(","),
      };
      this.cKey++;
      this.viewD = true;
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n;
      if (obj) {
        n = this[obj[fun]].call(this, obj, data);
      } else {
        n = this[fun].call(this, data);
      }
      return n;
    },
    closeshowDialog() {
      this.viewD = false;
    },
  },
};
</script>
<style scoped>
.w99 {
  width: 99%;
  margin: 0 auto;
}

.p10 {
  padding: 15px;
}

.tip {
  font-size: 13px;
  color: #a5a5a5;
  margin: 20px 0 10px;
}

.frequencyBox {
  display: flex;
  justify-content: space-around;
}

.frequencyBox p {
  width: 15%;
  line-height: 32px;
  text-align: center;
}

.frequencyBox > .el-input {
  width: 24%;
}

.frequencyBox > .el-input .el-input__inner {
  padding: 0 4px;
  text-align: center;
  border: 1px solid #dcdfe6;
}

.frequencyBox > .el-select {
  width: 29%;
}

.frequencyBox > .el-select .el-input__inner {
  padding: 0 4px 0 6px;
  border: 1px solid #dcdfe6;
}
.m-title {
  width: 100%;
  padding-left: 10px;
  border-left: 4px solid #39aef5;
  padding: 10px;
  font-size: 16px;
  color: #333;
  font-weight: 700;
  line-height: 30px;
  border-bottom: 1px solid #ebebeb;
  border-right: 1px solid #ebebeb;
}
/* 文本渲染区域 */
.cont {
  margin: 20px 0 0px 0;
  padding: 20px;
  border: 1px solid #aaa;
}
.cont .myh2 {
  text-align: center;
  color: #ff0000;
  border-bottom: 2px solid #ff0000;
  font-family: "华文中宋";
}
.cont .myh4 {
  text-align: center;
  font-family: "仿宋";
  font-size: 21px;
  font-weight: 700;
}

.cont .myspan {
  font-family: "仿宋";
  line-height: 24px;
  font-size: 21px;
}
.cont .mycontent {
  margin: 20px 0;
  font-family: "仿宋";
  font-size: 21px;
}
.cont .mytextarea {
  width: 100%;
  padding: 0 40px;
}
.cont .footDep {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-top: 1px solid #aaa;
  border-bottom: 1px solid #aaa;
  margin: 20px 0 10px;
}
::v-deep .el-radio-group{
  margin-left: 20px;
}
</style>